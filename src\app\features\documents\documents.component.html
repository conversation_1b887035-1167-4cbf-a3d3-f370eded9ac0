<div class="documents-container">
  <!-- Breadcrumb -->
  <app-breadcrumb
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium"
    divider=">"
    data-testid="breadcrumb"
    (onClickEvent)="onBreadcrumbClicked($event)">
  </app-breadcrumb>

 <div class="mb-4">

        <app-page-header title="DOCUMENTS.TITLE" [showCreateButton]="canAddDocument" [showSearch]="true" [showFilter]="false"
            createButtonText="DOCUMENTS.UPLOAD_DOCUMENT" searchPlaceholder="INVESTMENT_FUNDS.SEARCH_PLACEHOLDER"
            (create)="onUploadDocument($event)"
            (search)="onSearch($event)"></app-page-header>

    </div>
  <!-- Document Tabs -->
  <div *ngIf="!isLoading && documentCategories.length > 0" class="documents-content">
    <mat-tab-group
      [(selectedIndex)]="selectedTabIndex"
      (selectedTabChange)="onTabChange($event.index)"
      class="documents-tabs"
      data-testid="document-tabs">

      <mat-tab
        *ngFor="let category of documentCategories; let i = index"
        [label]="category.name ? category.name : ''">

        <!-- Document List Component - Only load when tab is active or has been loaded -->
        <app-document-list
          *ngIf="selectedTabIndex === i"
          [category]="category"
          [fundId]="currentFundId">
        </app-document-list>
      </mat-tab>
    </mat-tab-group>
  </div>

  <!-- Empty State for No Categories -->
  <div *ngIf="!isLoading && documentCategories.length === 0" class="empty-state">
    <div class="empty-state-content">
      <h3>{{ 'DOCUMENTS.NO_CATEGORIES' | translate }}</h3>
      <p>{{ 'DOCUMENTS.NO_CATEGORIES_MESSAGE' | translate }}</p>
    </div>
  </div>
</div>
