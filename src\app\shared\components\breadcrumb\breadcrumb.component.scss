@import "../../../../assets/scss/variables";

.breadcrumb-container {
  top: 2.25rem;
  display: flex;
  align-items: center;
  white-space: nowrap;
  width: fit-content;
  margin-bottom: 20px;
  .breadcrumb-item {
    cursor: pointer;

    &.active {
      font-weight: bold;
    }

    &:hover {
      color: $text-color;
      background-color: $neutral-background-hover;
    }

    &:focus {
      border: 2px solid $black;
      border-radius: 4px;
    }

    &:active {
      background-color: $border-dark;
    }

    .breadcrumb-icon {
      margin-inline-end: 4px;
      margin-inline-start: 0;
    }
  }

  .breadcrumb-divider {
    padding: 0 10px;
    color: $text-color;
    transform: none;
  }

  a {
    // padding: 0 10px;
    text-decoration: none;
    color: $text-color;
    transition: color 0.3s ease;
    cursor: pointer;
    text-align: left;
  }

  .disabled {
    cursor: not-allowed;
    font-weight: 700;
  }

  .breadcrumb-overflow {
    padding: 0 10px;
    cursor: pointer;
    color: $text-color;
    display: flex;
    align-items: center;
    position: relative;
  }

  html[dir="rtl"] & {
    .breadcrumb-item {
      .breadcrumb-icon {
        margin-inline-end: 0;
        margin-inline-start: 4px;
      }
    }

    .breadcrumb-divider {
      transform: scaleX(-1);
    }

    a {
      text-align: right;
    }
  }

  @media (min-width: 769px) {
    width: 25%;
  }
}


// Dropdown positioning
.dropdown {
  position: absolute;
  background-color: $white;
  border: 1px solid $border-light;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  top: 100%;

  a {
    display: block;
    padding: 5px 10px;
    color: $text-color;
    text-decoration: none;
    cursor: pointer;

    &:hover {
      background-color: $neutral-background-hover;
    }
  }

  .mdc-list-item {
    cursor: pointer !important;
  }

  // Default LTR
  left: 100%;
  right: auto;

  html[dir="rtl"] &,
  :root[dir="rtl"] & {
    right: 100%;
    left: auto;
  }
}

// Sizes
.breadcrumb-small {
  font-size: 12px;
  padding: 0 5px;
}

.breadcrumb-medium {
  font-size: 16px;
}

.breadcrumb-large {
  font-size: 22px;
  padding: 0 15px;
}
