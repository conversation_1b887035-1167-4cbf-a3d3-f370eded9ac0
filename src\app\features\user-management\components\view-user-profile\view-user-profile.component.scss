@import "../../../../shared/styles/form-container";
@import "../../../../../assets/scss/variables";

.view-user-profile-page {
  padding: 0;

  .profile-container {
    background: $card-background;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 0.5px solid #dce0e3;
    .border-bottom-gradient {
      border-bottom: 1px solid; /* Set the thickness */
      border-image: linear-gradient(to right, #e0e1e2 0%, #e0e1e2 15.63%);
      border-image-slice: 1;
    }
    .change-pass-btn {
      cursor: pointer;
    }

    .profile-photo-section {
      .photo-container {
        position: relative;
        display: inline-block;

        .profile-photo-circle {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          border: 3px solid $navy-blue;
          overflow: hidden;
          position: relative;
          background: $border-color;
          display: flex;
          align-items: center;
          justify-content: center;

          .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.3s ease;

            &.loading {
              opacity: 0.5;
            }

            &.error {
              opacity: 0.8;
            }
          }
        }
      }

      .photo-label {
        color: $dark-gray;
        font-size: 14px;
        margin: 0;
      }
    }

    .user-details-section {
      // View Mode Styles
      .form-label {
        font-weight: 700;
        color: $text-grey;
        font-size: 16px;
        margin-bottom: 8px;
        display: block;
      }

      .field-value {
        // padding: 12px 16px;
        font-size: 16px;
        font-weight: 600;
        color: $navy-blue;
        // min-height: 44px;
        display: flex;
        align-items: center;

        &:empty::before {
          content: "-";
          color: $light-dark;
        }

        .badge {
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;

          &.badge-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
          }

          &.badge-secondary {
            background-color: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
          }
        }

        i {
          color: $navy-blue;
        }

        // CV Download Attachment Card
        .attachment-card {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 8px 12px;
          border: 1px solid $border-color;
          border-radius: 6px;
          background: $white;
          transition: all 0.2s ease;
          width: 100%;
          max-width: 320px;

          &:hover {
            border-color: $navy-blue;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          .download-icon {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 4px;
            transition: background-color 0.2s ease;

            &:hover {
              background: $navy-blue;
            }

            img {
              width: 16px;
              height: 16px;
              filter: brightness(0.6);
            }

            &:hover img {
              filter: brightness(0) invert(1);
            }
          }

          .file-info {
            flex: 1;

            .file-name {
              font-size: 14px;
              color: $text-color;
              font-weight: 500;
            }
          }
        }
      }
    }

    .actions {
      display: flex;
      gap: 12px;
      // margin-top: 32px;
      // padding-top: 24px;
      // border-top: 1px solid $border-color;

      app-custom-button {
        min-width: 120px;
      }
    }
  }

  .loading-container {
    padding: 40px 20px;

    .spinner-border {
      width: 3rem;
      height: 3rem;
    }

    p {
      color: $dark-gray;
      margin-top: 16px;
      font-size: 14px;
    }
  }

  .error-container {
    padding: 40px 20px;

    .alert {
      max-width: 500px;
      margin: 0 auto 24px;
    }
  }
}

// RTL Support
[dir="rtl"] {
  .view-user-profile-page {
    .profile-container {
      .user-details-section {
        .field-value {
          text-align: right;

          .badge {
            margin-left: 0;
            margin-right: auto;
          }

          i {
            margin-left: 8px;
            margin-right: 0;
          }

          .attachment-card {
            flex-direction: row-reverse;
            text-align: right;

            .file-info {
              text-align: right;
            }
          }
        }
      }

      .actions {
        &.justify-content-end {
          justify-content: flex-start;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .view-user-profile-page {
    .profile-container {
      padding: 16px;

      .profile-photo-section {
        .photo-container {
          .profile-photo-circle {
            width: 100px;
            height: 100px;
          }
        }
      }

      .actions {
        flex-direction: column;

        app-custom-button {
          width: 100%;
        }
      }
    }
  }
}

.role-badge {
  font-size: 12px;
}
