import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';
import { SuperAdminGuard } from '@core/guards/super-admin.guard';

export const USER_MANAGEMENT_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./user-management.component').then(m => m.UserManagementComponent),
    canActivate: [AuthGuard, SuperAdminGuard]
  },
  {
    path: 'create',
    loadComponent: () =>
      import('./create-user/create-user.component').then(m => m.CreateUserComponent),
    canActivate: [AuthGuard, SuperAdminGuard]
  },
  {
    path: 'edit/:id',
    loadComponent: () =>
      import('./components/edit-user/edit-user.component').then(m => m.EditUserComponent),
    canActivate: [AuthGuard, SuperAdminGuard]
  },
  {
    path: 'details/:id',
    loadComponent: () =>
      import('./components/view-user-profile/view-user-profile.component').then(m => m.ViewUserProfileComponent),
    canActivate: [AuthGuard, SuperAdminGuard]
  },
  {
    path: 'profile',
    loadComponent: () =>
      import('./components/update-user-profile/update-user-profile.component').then(m => m.UpdateUserProfileComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'my-profile',
    loadComponent: () =>
      import('./components/view-user-profile/view-user-profile.component').then(m => m.ViewUserProfileComponent),
    canActivate: [AuthGuard]
  }
];
