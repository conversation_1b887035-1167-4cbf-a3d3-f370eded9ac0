.document-upload-dialog {
  min-width: 500px;
  max-width: 600px;

  .upload-content {
    // padding: 20px 0;
    min-height: 300px;

    .form-container {
      margin-bottom: 24px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .file-upload-section {
      margin: 24px 0;

      .upload-label {
        display: block;
        font-weight: 500;
        color: #4a5568;
        margin-bottom: 8px;
        font-size: 14px;
      }
    }

    .upload-progress {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background-color: #f7fafc;
      border-radius: 8px;
      margin-top: 16px;

      span {
        color: #4a5568;
        font-size: 14px;
      }
    }
  }

  .dialog-actions {
    padding: 16px 24px;
    border-top: 1px solid #e2e8f0;
    gap: 12px;
  }
}

// RTL Support
[dir="rtl"] {
  .document-upload-dialog {
    .upload-content {
      .upload-progress {
        flex-direction: row-reverse;
      }
    }

    .dialog-actions {
      flex-direction: row-reverse;
    }
  }
}
