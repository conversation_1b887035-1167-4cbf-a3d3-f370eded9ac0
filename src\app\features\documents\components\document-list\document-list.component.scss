.document-list-container {

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        gap: 16px;

        p {
            color: #718096;
            margin: 0;
        }
    }

    .empty-state {
        text-align: center;
        padding: 48px 24px;
        color: #718096;

        .empty-icon {
            margin-bottom: 16px;

            mat-icon {
                font-size: 48px;
                width: 48px;
                height: 48px;
                color: #cbd5e0;
            }
        }

        h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #4a5568;
        }

        p {
            margin: 0;
            font-size: 14px;
        }
    }

    .documents-table {
        background: white;
        border-radius: 16px; /* Increased from 8px to 16px for more rounded corners */
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        padding: 16px; /* Added padding around the entire table */

        ::ng-deep {
            .mat-mdc-table {
                border-collapse: separate;
                border-spacing: 0 16px !important;

                th,
                td {
                    font-weight: 700;
                    font-size: 16px;
                    line-height: 24px;
                    padding: 20px 16px; /* Increased padding from 16px 8px to 20px 16px */

                    &:first-of-type {
                        border-radius: 0 12px 12px 0; /* Increased from 8px to 12px */
                        border-inline-start: 1px solid #EAEEF1;

                        &:lang(en) {
                            border-radius: 12px 0 0 12px; /* Increased from 8px to 12px */
                        }
                    }

                    &:last-of-type {
                        border-radius: 12px 0 0 12px; /* Increased from 8px to 12px */
                        border-inline-end: 1px solid #EAEEF1;

                        &:lang(en) {
                            border-radius: 0 12px 12px 0; /* Increased from 8px to 12px */
                        }
                    }
                }
            }

            .mat-mdc-table thead {
                background-color: #00205A !important;

                th {
                    color: white !important;
                }
            }

            .mat-mdc-table tbody {
                td {
                    color: #717577 !important;
                    border: 1px solid #EAEEF1;
                    border-right: 0;
                    border-left: 0;
                    line-height: 20px;
                    padding: 16px; /* Increased padding from 8px to 16px */
                }
            }
        }
    }
}

// RTL Support
[dir="rtl"] {
    .document-list-container {
        .empty-state {
            text-align: center;
        }
    }
}