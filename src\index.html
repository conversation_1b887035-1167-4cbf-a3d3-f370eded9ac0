<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>Jadwa</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="Jadwa application">
  <link rel="icon" type="image/x-icon" href="./assets/images/favicon-blue.svg">
  <link rel="manifest" href="manifest.json">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">

  <!-- Language & Direction Handler -->
  <script>
    (function () {
      // Get language from localStorage, parse it properly, default to Arabic
      let lang;
      try {
        const storedLang = localStorage.getItem('lang');
        if (storedLang && storedLang !== '{}') {
          lang = JSON.parse(storedLang);
        } else {
          lang = 'ar'; // Default to Arabic
        }
      } catch (e) {
        lang = 'ar'; // Default to Arabic if parsing fails
      }

      const dir = lang === 'ar' ? 'rtl' : 'ltr';

      document.documentElement.lang = lang;
      document.documentElement.dir = dir;

      document.addEventListener('DOMContentLoaded', function () {
        document.body.setAttribute('lang', lang);
        document.body.setAttribute('dir', dir);
      });
    })();
  </script>
</head>
<body class="mat-typography">
  <app-root></app-root>
</body>
</html>
