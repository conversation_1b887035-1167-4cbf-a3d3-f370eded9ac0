<div class="members">
  <!-- Breadcrumb -->
  <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"

      divider=">">
    </app-breadcrumb>
  </div>
  <!-- Page Header -->
  <div class="mb-4">
    <app-page-header
      title="INVESTMENT_FUNDS.MEMBERS.MEMBERS"
      [createButtonDisabled]="isMaxMembersReached"
      [showSearch]="true"
      [showFilter]="true"
      [showCreateButton]="isHasPermissionAdd"
      createButtonText="INVESTMENT_FUNDS.MEMBERS.ADD_MEMBER"
      searchPlaceholder="INVESTMENT_FUNDS.MEMBERS.SEARCH"
      (create)="addNewMember()" [exceedMaxNumber]="isMaxMembersReached"
      (search)="onSearch($event)"
      (filter)="openFilter()">

      <div slot="between"  class="w-100 mx-3" *ngIf="isMaxMembersReached">
        <app-alert [hasClose]="false" [isStaticPosition]="true" [alertType]="AlertType.Info" [msg]="'INVESTMENT_FUNDS.MEMBERS.MAX_MEMBERS_REACHED' | translate"></app-alert>
      </div>
    </app-page-header>
  </div>

  <!-- Maximum Members Warning -->



  <!-- <div class="alert alert-warning mb-3" *ngIf="isMaxMembersReached">
    <div class="d-flex align-items-center">
      <img src="assets/images/warning.png" alt="Warning" class="me-2" style="width: 20px; height: 20px;">
      <span>{{ 'INVESTMENT_FUNDS.MEMBERS.MAX_MEMBERS_REACHED' | translate }}</span>
    </div>
  </div> -->

  <!-- Loading State -->
  <!-- <div class="loading-container text-center py-5" *ngIf="isLoading">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3 text-muted">{{ 'COMMON.LOADING' | translate }}</p>
  </div> -->

  <!-- Error State -->
  <div class="error-container text-center py-5" *ngIf="hasError && !isLoading">
    <div class="error-icon mb-3">
      <img src="assets/images/error-icon.png" alt="Error" style="width: 48px; height: 48px;">
    </div>
    <h5 class="text-danger">{{ 'INVESTMENT_FUNDS.MEMBERS.LOAD_ERROR' | translate }}</h5>
    <p class="text-muted">{{ errorMessage | translate }}</p>
    <button type="button" class="btn btn-primary" (click)="loadMembers()">
      {{ 'COMMON.RETRY' | translate }}
    </button>
  </div>

  <!-- Empty State -->
  <div class="empty-state text-center py-5" *ngIf="!isLoading && !hasError && !hasMembers">
    <div class="empty-icon mb-3">
      <img src="assets/images/nodata.png" alt="No Members" style="width: 64px; height: 64px;">
    </div>
    <h5>{{ 'INVESTMENT_FUNDS.MEMBERS.NO_MEMBERS' | translate }}</h5>
    <p class="text-muted">{{ 'INVESTMENT_FUNDS.MEMBERS.NO_MEMBERS_MESSAGE' | translate }}</p>
    <button
      type="button"
      class="btn btn-primary"
      *ngIf="isHasPermissionAdd"
      [disabled]="isMaxMembersReached"
      (click)="addNewMember()">
      <img src="assets/icons/add-icon.png" alt="Add" class="me-2" style="width: 16px; height: 16px;">
      {{ 'INVESTMENT_FUNDS.MEMBERS.ADD_MEMBER' | translate }}
    </button>
  </div>

  <!-- Members List -->
  <div class="members-list" *ngIf="!isLoading && !hasError && hasMembers">
    <div class="row">
      <div class="col-12 col-md-6 col-lg-4" *ngFor="let member of filteredMembers; trackBy: trackByMemberId">
        <app-member-card
          [member]="member"
          [canEdit]="(isLegalCouncil || isBoardSecretary)"
          [canDelete]="(isLegalCouncil || isBoardSecretary)"
          (edit)="onEditMember($event)"
          (delete)="onDeleteMember($event)">
        </app-member-card>
      </div>
    </div>

    <!-- Members Count Info -->
    <!-- <div class="members-info mt-4 p-3 bg-light rounded">
      <div class="row">
        <div class="col-md-6">
          <small class="text-muted">
            {{ 'COMMON.TOTAL_ITEMS' | translate }}: {{ totalCount }}
          </small>
        </div>
        <div class="col-md-6 text-md-end">
          <small class="text-muted">
            {{ 'COMMON.SHOWING' | translate }} {{ filteredMembers.length }} {{ 'COMMON.OF' | translate }} {{ totalCount }}
          </small>
        </div>
      </div>
    </div> -->
  </div>
</div>
