import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

export type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'outline';
export type ButtonSize = 'small' | 'medium' | 'large';
export type ButtonHtmlType = 'button' | 'submit' | 'reset';

@Component({
  selector: 'app-custom-button',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './custom-button.component.html',
  styleUrl: './custom-button.component.scss'
})
export class CustomButtonComponent {
  // Legacy inputs for backward compatibility
  @Input() iconName: IconEnum | undefined;
  @Input() btnName: string = '';
  @Input() class: string = '';
  @Input() buttonType: ButtonTypeEnum = ButtonTypeEnum.Primary;

  // New inputs for modern usage
  @Input() label: string = '';
  @Input() variant: ButtonVariant = 'primary';
  @Input() size: ButtonSize = 'medium';
  @Input() type: ButtonHtmlType = 'button';
  @Input() loading: boolean = false;
  @Input() disabled: boolean = false;
  @Input() icon: string = '';

  @Output() click = new EventEmitter<any>();

  IconEnum = IconEnum;
  ButtonTypeEnum = ButtonTypeEnum;

  // Computed properties for template
  get displayLabel(): string {
    return this.label || this.btnName;
  }

  get displayIcon(): string {
    return this.icon || this.iconName || '';
  }

  get computedVariant(): ButtonVariant {
    // Map legacy buttonType to new variant
    switch (this.buttonType) {
      case ButtonTypeEnum.Primary:
        return 'primary';
      case ButtonTypeEnum.Secondary:
        return 'secondary';
      case ButtonTypeEnum.Danger:
        return 'danger';
      case ButtonTypeEnum.OutLine:
        return 'outline';
      default:
        return this.variant;
    }
  }

  get isDisabled(): boolean {
    return this.disabled || this.loading;
  }

  action(event?: Event) {
    if (!this.isDisabled) {
      // For backward compatibility, emit event only if it exists
      // This allows parent components to optionally handle event prevention
      if (event) {
        this.click.emit(event);
      } else {
        this.click.emit();
      }
    }
  }
}
