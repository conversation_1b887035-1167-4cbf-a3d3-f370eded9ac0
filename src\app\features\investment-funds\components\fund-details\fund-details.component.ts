import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { FundCardInfoComponent } from '../../../../shared/components/fund-card-info/fund-card-info.component';
import { FundHistoryComponent } from '../../../../shared/components/fund-history/fund-history.component';
import { MatDialog } from '@angular/material/dialog';
import { EditFundExitDateComponent } from '../../../edit-fund-exit-date/edit-fund-exit-date.component';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { FundsService } from '../../services/fund.service';
import Swal from 'sweetalert2';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AlertComponent } from "../../../../shared/components/alert/alert.component";
import { DateHijriConverterPipe } from "../../../../shared/pipes/dateHijriConverter/dateHijriConverter.pipe";
import { Router } from '@angular/router';
import { AlertType } from '@core/enums/alert-type';
import { LanguageService } from '../../../../core/gl-services/language-services/language.service';
import { BreadcrumbComponent } from "../../../../shared/components/breadcrumb/breadcrumb.component";
import { SizeEnum } from '@shared/enum/size-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { FundStatus } from '@shared/enum/fund-status';
import { RouterOutlet } from '@angular/router';
import { FundsServiceProxy, NotificationModule } from '@core/api/api.generated';

@Component({
  selector: 'app-fund-details',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FundCardInfoComponent,
    FundHistoryComponent,
    AlertComponent,
    DateHijriConverterPipe,
    BreadcrumbComponent,
    RouterOutlet
],
  templateUrl: './fund-details.component.html',
  styleUrls: ['./fund-details.component.scss'],
})
export class FundDetailsComponent implements OnInit {
  fundDetails :any;

  isExpanded: boolean = true;
currentPage: number = 1;
pageSize: number = 10;
totalNotifications: number = 0;
  fundCards: any[] = [];
  AlertType = AlertType
currentLang: any;
fundStatus = FundStatus;
breadcrumbSizeEnum = SizeEnum;
breadcrumbItems!: IBreadcrumbItem[] ;
  fundId!: number;
  @ViewChild(FundHistoryComponent) fundStatusHistory!: FundHistoryComponent;

  constructor(
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private fundsService: FundsService,
   private translateService: TranslateService,
   private router:Router,
   public tokenService:TokenService,
   private fundProxy:FundsServiceProxy

  ) {

    this.currentLang = JSON.parse(localStorage.getItem('lang') || LanguageEnum.ar);
    localStorage.getItem('lang')
  }
   fundNotifications: any[] = []
  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      console.log(params);
       this.fundId = +params['id'];
      this.loadFundDetails(this.fundId);

    });
  }

  initializeFundCards() {
    const memberCount = this.fundDetails?.membersCount ?? 0;

    this.fundCards = [
      {
        notificationCount: 'resolutionsNotificationCount',
        fundCountKey: 'resolutionsCount',
        title:  'FUND_DETAILS.RESOLUTIONS',
        icon: 'assets/images/Featured icon.png',
        routerLink: '/admin/investment-funds/resolutions',
        queryParams: { fundId: this.fundId },
        disabled:memberCount === 0,
        moduleId:NotificationModule._3
      },
      {
        notificationCount: 'evaluationsNotificationCount',
        fundCountKey: 'evaluationsCount',
        title:  'FUND_DETAILS.RATINGS',
        icon: 'assets/images/reviews.png',
        routerLink: '/decisions',
        disabled: memberCount === 0,
        moduleId:NotificationModule._4
      },
      {
        notificationCount: 'documentsNotificationCount',
        fundCountKey: 'documentsCount',
        title:  'FUND_DETAILS.DOCUMENTS',
        icon: 'assets/images/document.png',
        routerLink: '/admin/investment-funds/documents',
        queryParams: { fundId: this.fundId },
        disabled: memberCount === 0,
        moduleId:NotificationModule._5

      },
      {
        notificationCount: 'meetingsNotificationCount',
        fundCountKey: 'meetingsCount',
        title:  'FUND_DETAILS.MEETINGS',
        icon: 'assets/images/meeting.png',
        routerLink: '/decisions',
        disabled: memberCount === 0,
        moduleId:NotificationModule._6
      },
      {
        notificationCount: 'membersNotificationCount',
        fundCountKey: 'membersCount',
        title:  'FUND_DETAILS.MEMBERS',
        icon: 'assets/images/members.png',
        routerLink: 'admin/investment-funds/members',
        queryParams: { fundId: this.fundId },
        disabled: false,
        moduleId:NotificationModule._2
      }
    ];
  }



  loadFundDetails(fundId: any) {
    this.fundsService
      .getFundDetailsById(fundId)
      .subscribe((res:any) =>
      {
        if(res.successed){
        this.fundDetails = res.data;
        this.fundNotifications = res.data.fundNotifications;
        this.totalNotifications = res.data.fundNotifications.length;
        this.breadcrumbItems = [
          {
            label: 'INVESTMENT_FUNDS.TITLE',
            url: '/admin/investment-funds',
            icon: 'fas fa-home',
          },
          {
            label: this.fundDetails.name,
            url: '/admin/investment-funds/create',
            disabled: true,
          },
        ];
        this.initializeFundCards();
        }
      }
      );
    console.log('Loading fund details for ID:', fundId);
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }

  formatHijriDate(date: string): string {
    return moment(date, 'DD/MM/YYYY').format('iD iMMMM iYYYY');
  }

  editFundExitDate(id:number) {
    debugger
    console.log(localStorage.getItem('lang'));

    const currentLang = JSON.parse(
      localStorage.getItem('lang') || LanguageEnum.ar
    );

    const dialogRef = this.dialog.open(EditFundExitDateComponent, {
      width: '528px',
      direction: currentLang,
      data: {
      initiationDateGregorian: this.fundDetails?.initiationDate,
      initiationDateHijri: this.fundDetails?.initiationDateHijri
      }
    });
    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
      let obj = {
        id,
        exitDate: result.date
      };
      this.fundsService.editFundExitDate(obj).subscribe({
        next: (res: any) => {
        if (res.successed) {
          Swal.fire({
          icon: 'success',
          title: this.translateService.instant(
            'INVESTMENT_FUNDS.SUCCESS_SAVED'
          ),
          showConfirmButton: false,
          timer: 1500,
          });
          this.loadFundDetails(this.fundId);
        } else {
          Swal.fire({
          icon: 'error',
          title: this.translateService.instant(
            'FUND_STRATEGIES.ERROR_UNEXPECTED'
          ),
          showConfirmButton: false,
          timer: 1500,
          });
        }
        },
        error: () => {
        Swal.fire({
          icon: 'error',
          title: this.translateService.instant(
          'FUND_STRATEGIES.ERROR_UNEXPECTED'
          ),
          showConfirmButton: false,
          timer: 1500,
        });
        }
      });
      }
    });
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case FundStatus.New: // New
        return 'status-new';
      case FundStatus.UnderConstruction: // UnderConstruction
        return 'status-under-construction';
      case FundStatus.WaitingForAddingMembers: // WaitingForAddingMembers
        return 'status-waiting';
      case FundStatus.Active: // Active
        return 'status-active';
      case FundStatus.Exited: // Exited
        return 'status-exited';
      default:
        return '';
    }
  }
  goToDetails(id:number){
    console.log('id',id);
    this.router.navigate(['/admin/investment-funds/update', id]);
  }

  handleBreadcrumbClick(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigate([item.url]);
    }
  }
  filterNotification(moduleId?:number)
  {
    this.fundDetails.fundNotifications = this.fundNotifications;
    if(moduleId)
    this.fundDetails.fundNotifications = this.fundDetails.fundNotifications.filter((n:any) => n.moduleId == moduleId);

    this.fundStatusHistory.apiResponse = this.fundDetails;
    this.fundStatusHistory.loadDataFromApi();
  }

loadMoreNotification() {
this.currentPage ++;
console.log('loadMoreNotification',this.currentPage);
this.fundProxy.getAllFundNotification(this.fundId,  this.pageSize,this.currentPage ).subscribe({
    next: (res: any) => {
      if (res.successed) {
        console.log('loadMoreNotification',res.data);
        const newNotifications = res.data.items || res.data;
        this.fundNotifications.push(...newNotifications); // Adjust if your API returns { items, totalCount }
        this.totalNotifications = res.data.totalCount || this.fundNotifications.length;
        this.filterNotification();
      }
    },
    error: () => {
      Swal.fire({
        icon: 'error',
        title: this.translateService.instant('FUND_STRATEGIES.ERROR_UNEXPECTED'),
        showConfirmButton: false,
        timer: 1500,
      });
    }
  });
}
}
