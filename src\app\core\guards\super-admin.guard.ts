import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { ErrorModalService } from '@core/services/error-modal.service';
import { TranslateService } from '@ngx-translate/core';

@Injectable({ providedIn: 'root' })
export class SuperAdminGuard implements CanActivate {
  constructor(
    private tokenService: TokenService,
    private router: Router,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    // Check if user is authenticated first
    if (!this.tokenService.isLoggedIn()) {
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check if user has super admin role
    if (this.tokenService.hasRole('superadmin')) {
      return true;
    }

    // If user doesn't have super admin role, show error and redirect to dashboard
    this.errorModalService.showError(
      this.translateService.instant('COMMON.ACCESS_DENIED_SUPER_ADMIN_REQUIRED')
    );
    this.router.navigate(['/admin/dashboard']);
    return false;
  }
}
