import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DateHijriConverterPipe } from "../../pipes/dateHijriConverter/dateHijriConverter.pipe";
import moment from 'moment';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

interface TimelineItem {
  title: string;
  description: string;
  date: string;
  hijriDate: string;
  time: string;
  type:  'fundHistory' | 'fundNotifications';
  itemtype: number;
  status?: 'active' | 'inactive';
  createdAt: string;
  statusId:number;

}

@Component({
  selector: 'app-fund-history',
  standalone: true,
  imports: [CommonModule, DateHijriConverterPipe,TranslateModule],
  templateUrl: './fund-history.component.html',
   styleUrls: ['./fund-history.component.scss']
})
export class FundHistoryComponent implements OnInit {
  activeTab: string = 'history';
  filteredItems: TimelineItem[] = [];
  allItems: TimelineItem[] = [];
  @Input() apiResponse:any;
  period!: string;
  time!: string;
  @Output() resetFilterNotification = new EventEmitter<void>();
  @Output() loadMoreNotification = new EventEmitter<void>();
  notificationTotalCount: any;

  constructor(private translateService: TranslateService){}

  ngOnInit(): void {
    this.loadDataFromApi();
  }
  formatDateToString(dateTime: string): string {
    return moment(dateTime).format('YYYY-MM-DD');
  }
  loadDataFromApi(): void {
   this.notificationTotalCount = this.apiResponse.notificationTotalCount
    const fundNotifications = this.apiResponse?.fundNotifications?.map((n:any) => ({
      title: n.title,
      description: n.message,
      date: this.formatDate(n.createdAt),
      time: this.formatTime(n.createdAt),
      type: 'fundNotifications',
      itemtype: n.notificationType,
      status: 'active',
      createdAt: n.createdAt

    }));

    const fundHistory = this.apiResponse?.fundHistory?.map((h:any) => ({
      title: h.userName,
      status: h.statusName,
      statusId: h.statusId,
      description: h.roleName ?? '',
      date: this.formatDate(h.createdAt),
      time: this.formatTime(h.createdAt),
      type: 'fundHistory',
      createdAt: h.createdAt

    }));

    this.allItems = [...fundNotifications, ...fundHistory];
    this.setActiveTab(this.activeTab);
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
    this.filteredItems = this.allItems.filter(
      item => item.type === (tab === 'details' ? 'fundHistory' : 'fundNotifications')
    );
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB');
  }

  formatTime(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' ,    hour12: true
  });
  }

  getIconForType(type: number): string {
      switch (type) {
      case 1: // Fund notifications
        return 'assets/images/gray-icon.svg';
      case 2: // User notifications
        return 'assets/images/gray-icon.svg';
      case 3 :
        return 'assets/images/red-icon.svg';
      case 4 :
        return 'assets/images/green-icon.svg';
      case 5 :
        return 'assets/images/blue-icon.svg';
      case 6 :
        return 'assets/images/yellow-icon.svg';
      case 7 :
        return 'assets/images/green-icon.svg';
      case 8 :
        return 'assets/images/gray-icon.svg';
      case 9 :
        return 'assets/images/gray-icon.svg';
      case 22 :
        return 'assets/images/green-icon.svg';
      default:
        return 'assets/images/notify-red.svg';
    }
  }

  getStatusClass(statusId: number): string {
    console.log(statusId);

    switch (statusId) {
      case 0: // New
        return 'status-new';
      case 1: // UnderConstruction
        return 'status-under-construction';
      case 2: // WaitingForAddingMembers
        return 'status-waiting';
      case 3: // Active
        return 'status-active';
      case 4: // Exited
        return 'status-exited';
      default:
        return '';
    }
  }

  formatTimetoOtherSide(value: string) {
    const [period, time] = value.split(' ');
    this.period = period;
    this.time = time;
   // return `${period}  ${time}  `;
  }

  getPeriod(item: any): string {
    return item.time.split(' ')[0];
  }

  getTime(item: any): string {
    return item.time.split(' ')[1];
  }
  resetNotificationFilter()
  {
    this.resetFilterNotification.emit();
  }
  loadMoreNotifications()
  {
    this.loadMoreNotification.emit();
  }
}
