<div class="form-container p-4 mt-3">
    <h3 class="header font-weight-bold">
        <span>
            <img src="assets/images/filter-1.png" alt="filter">
        </span>
        {{'INVESTMENT_FUNDS.FORM.FILTER' | translate}}
    </h3>
    <app-form-builder [formControls]="formControls" [formGroup]="formGroup" [isFormSubmitted]="isFormSubmitted"
        (dateSelected)="dateSelected($event)">
    </app-form-builder>


    <div class="mt-3 row align-items-center justify-content-between">
        <div class="col-12 mb-1">
            <app-custom-button class="w-100 py-1 fs-14" [btnName]="'COMMON.APPLAY' | translate"
                [buttonType]="buttonEnum.Primary" [iconName]="IconEnum.verify" (click)="applyFilters()">
            </app-custom-button>

        </div>

        <div class="col-6">
            <app-custom-button class="w-100 p-1 fs-14" [btnName]="'COMMON.RESET' | translate"
                [buttonType]="buttonEnum.OutLine" [iconName]="IconEnum.reset" (click)="resetFilters()">
            </app-custom-button>
        </div>
        <div class="col-6">
            <app-custom-button class="w-100 py-1 fs-14" [btnName]="'COMMON.CANCEL' | translate" (click)="closeDialog()"
                [buttonType]="buttonEnum.Secondary" [iconName]="IconEnum.cancel">
            </app-custom-button>
        </div>

    </div>
</div>