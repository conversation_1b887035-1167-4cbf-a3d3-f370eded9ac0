<div class="document-viewer-dialog">
  <!-- Header -->
  <div class="viewer-header">
    <div class="document-info">
      <div class="file-details">
        <h3 class="file-name">{{ getDocumentDisplayName() }}</h3>
        <p class="file-size">{{ formatFileSize(data.document.fileSize || 0) }}</p>
      </div>
    </div>
    
    <div class="header-actions">
      
      
      <button mat-icon-button (click)="onClose()" class="close-button">
        <mat-icon>X</mat-icon>
      </button>
    </div>
  </div>

  <!-- Content -->
  <div class="viewer-content">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner></mat-spinner>
      <p>{{ 'DOCUMENTS.LOADING_PREVIEW' | translate }}</p>
    </div>

    <!-- Error State -->
    <div *ngIf="!isLoading && errorMessage" class="error-container">
      <mat-icon class="error-icon">error_outline</mat-icon>
      <h4>{{ 'DOCUMENTS.PREVIEW_FAILED' | translate }}</h4>
      <p>{{ errorMessage | translate }}</p>
      <p class="error-details">{{ 'DOCUMENTS.PREVIEW_ERROR_DETAILS' | translate }}</p>

      <div class="error-actions">
        <app-custom-button
          [btnName]="'DOCUMENTS.DOWNLOAD_INSTEAD' | translate"
          (click)="onDownload()"
          [buttonType]="buttonEnum.Primary"
          [iconName]="iconEnum.arrowRight">
        </app-custom-button>

        <app-custom-button
          [btnName]="'COMMON.RETRY' | translate"
          (click)="loadDocumentPreview()"
          [buttonType]="buttonEnum.Secondary"
          [iconName]="iconEnum.reset">
        </app-custom-button>
      </div>
    </div>

    <!-- Preview Content -->
    <div *ngIf="!isLoading && !errorMessage" class="preview-container">
      <!-- Supported File Preview -->
      <iframe 
        *ngIf="isPreviewSupported() && previewUrl"
        [src]="previewUrl"
        class="document-preview"
        frameborder="0">
      </iframe>

      <!-- Unsupported File Type -->
      <div *ngIf="!isPreviewSupported()" class="unsupported-preview">
        <mat-icon class="large-icon">{{ getFileIcon() }}</mat-icon>
        <h4>{{ 'DOCUMENTS.PREVIEW_NOT_SUPPORTED' | translate }}</h4>
        <p>{{ 'DOCUMENTS.PREVIEW_NOT_SUPPORTED_MESSAGE' | translate }}</p>
        
        <app-custom-button
          [btnName]="'DOCUMENTS.DOWNLOAD_TO_VIEW' | translate"
          (click)="onDownload()"
          [buttonType]="buttonEnum.Primary"
          [iconName]="iconEnum.arrowRight">
        </app-custom-button>
      </div>
    </div>
  </div>
</div>
