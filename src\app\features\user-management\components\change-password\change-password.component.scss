.change-password-section {

  padding: 24px;
  margin-bottom: 24px;

  .section-header {
    padding-bottom: 16px;

    .section-title {
      color: #002447;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .section-description {
      color: #6c757d;
      font-size: 14px;
      margin-bottom: 0;
    }
  }

  .form-fields-section {
    margin-top: 24px;
      .title {
      font-weight: 500;
      font-size: 16px;
     }
      .title-rule{ font-size: 12px !important;
    color: #8b8b8b;
    line-height: 126%;
    margin-bottom: 0;}
  }

  .password-rules-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;

    .password-rules-header {
      display: flex;
      align-items: center;
      color: #495057;
      font-weight: 500;
      margin-bottom: 8px;

      .fas {
        color: #007bff;
      }

      .rules-title {
        font-size: 14px;
      }
    }

    .password-rules {
      margin-bottom: 0;

      .title{font-weight: 500;}
      .title-rule{color:red}

      li {
        color: #6c757d;
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 4px;
        padding-left: 16px;
        position: relative;

        &:before {
          content: '•';
          color: #007bff;
          position: absolute;
          left: 0;
          top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 16px;

    app-custom-button {
      min-width: 120px;
    }
  }

.change-password-btn{
  padding:0 12px ;

}
  // Loading state
  .loading-overlay {
    position: relative;

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .change-password-section {
    padding: 16px;
    margin-bottom: 16px;

    .actions {
      flex-direction: column;
      gap: 8px;

      app-custom-button {
        width: 100%;
      }
    }
  }
}

// RTL support
[dir="rtl"] {
  .change-password-section {
    .password-rules-container {
      .password-rules {
          .title{font-weight: 500;}
      .title-rule{color:red}
        li {
          padding-left: 0;
          padding-right: 16px;

          &:before {
            left: auto;
            right: 0;
          }
        }
      }
    }
  }
}
