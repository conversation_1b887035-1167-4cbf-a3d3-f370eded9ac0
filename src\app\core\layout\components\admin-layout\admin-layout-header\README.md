# Admin Layout Header Component

A modern, responsive header component for the admin layout with search functionality, user profile, and notifications.

## Features

### 🎨 **Modern Design**
- Beautiful gradient background with subtle texture
- Glass-morphism effects with backdrop blur
- Smooth animations and hover effects
- Professional and clean appearance

### 🔍 **Search Functionality**
- Centered search bar with filter options
- Real-time search with enter key support
- Responsive design that adapts to screen size
- RTL (Right-to-Left) text direction support

### 👤 **User Profile Section**
- User avatar with name and role display
- Dropdown toggle for user menu
- Role-based information display
- Responsive user info (hidden on mobile)

### 🔔 **Notification System**
- Real-time notification count badge
- Visual notification indicator
- API integration for unread notifications
- Responsive notification button

### 🌐 **Language Support**
- Language switcher with flag icons
- Internationalization support
- Smooth language transitions
- Persistent language preference

### 📱 **Responsive Design**
- Mobile-first approach
- Adaptive layout for all screen sizes
- Touch-friendly interface elements
- Optimized for tablets and phones

## Usage

### Basic Implementation
```html
<app-admin-layout-header 
  (menuToggle)="onMenuToggle()"
  (searchEvent)="onSearch($event)"
  (filterEvent)="onFilterOpen()">
</app-admin-layout-header>
```

### Event Handling
```typescript
// In parent component
onMenuToggle(): void {
  // Handle mobile menu toggle
}

onSearch(query: string): void {
  // Handle search functionality
  console.log('Search query:', query);
}

onFilterOpen(): void {
  // Handle filter dialog opening
}
```

## Component Properties

### Inputs
- `notificationCount`: Number of unread notifications
- `userName`: Current user's display name
- `roleName`: Current user's role
- `searchQuery`: Current search query

### Outputs
- `menuToggle`: Emitted when mobile menu is toggled
- `searchEvent`: Emitted when search is performed
- `filterEvent`: Emitted when filter button is clicked

## Styling

### CSS Variables
The component uses SCSS variables from the global variables file:
- `$navy-blue`: Primary brand color
- `$notification-badg`: Notification badge color

### Responsive Breakpoints
- **Desktop**: > 992px (Full layout with all features)
- **Tablet**: 768px - 991px (Condensed layout)
- **Mobile**: < 768px (Minimal layout with mobile menu)

## API Integration

### Notification Service
```typescript
getUserNotificationUnreaded(): void {
  this.notificationServiceProxy.unReadedNotificationList(1, 5, undefined, 'CreatedAt desc')
    .subscribe((res) => {
      this.notificationCount = res.totalCount;
    });
}
```

### User Information
```typescript
initializeUserInfo(): void {
  const decodedToken = this.TokenService.getDecodedToken();
  if (decodedToken) {
    this.userName = decodedToken.sub || 'User';
  }
}
```

## Customization

### Colors
Modify the gradient background in SCSS:
```scss
.header-container {
  background: linear-gradient(135deg, #your-color-1 0%, #your-color-2 100%);
}
```

### Search Placeholder
Update translation files:
```json
{
  "HEADER": {
    "SEARCH_PLACEHOLDER": "Your custom placeholder..."
  }
}
```

### User Avatar
Replace the default avatar path in the template:
```html
<img src="path/to/your/avatar.jpg" class="user-avatar" alt="User Avatar" />
```

## Dependencies

- `@angular/forms` (FormsModule for search input)
- `@ngx-translate/core` (Internationalization)
- `FontAwesome` (Icons)
- Custom services: `TokenService`, `LanguageService`, `NotificationServiceProxy`

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

Supports all modern browsers with CSS Grid and Flexbox capabilities.
