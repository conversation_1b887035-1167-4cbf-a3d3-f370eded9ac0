import { Component, inject, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AdminLayoutHeaderComponent } from './admin-layout-header/admin-layout-header.component';
import { BreadcrumbComponent } from "../../../../shared/components/breadcrumb/breadcrumb.component";
import { TableComponent } from "../../../../shared/components/table/table.component";
import { AdminLayoutSideNavComponent } from './admin-layout-side-nav/admin-layout-side-nav.component';
import { Messaging, getMessaging, getToken, onMessage, isSupported } from '@angular/fire/messaging';
import { environment } from '../../../../../environments/environment';
import { DateHijriConverterPipe } from "../../../../shared/pipes/dateHijriConverter/dateHijriConverter.pipe";
import { AuthenticationServiceProxy, UpdateFCMTokenCommand } from '@core/api/api.generated';
import { TokenService } from 'src/app/features/auth/services/token.service';
@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    AdminLayoutHeaderComponent, BreadcrumbComponent, TableComponent,
    AdminLayoutSideNavComponent,
    DateHijriConverterPipe
],
  templateUrl: './admin-layout.component.html',
  styleUrls: ['./admin-layout.component.scss']
})
export class AdminLayoutComponent implements OnInit {
  notification: {
  title: string;
  description: string;
  type: string;
  status: string;
  statusId: number;
  time: string;
  createdAt: Date;
  date: string;
} | null = null;
  constructor(private AuthService: AuthenticationServiceProxy,public tokenService : TokenService) {
  }

 async ngOnInit()  {
    try {
      const isMessagingSupported = await isSupported();
      if (isMessagingSupported) {
        console.log('Firebase messaging is supported in this browser');
        this.registerServiceWorker();
        this.requestNotificationPermission();
        this.setupMessageListener();
      } else {
        console.warn('Firebase messaging is not supported in this browser');
      }
    } catch (error) {
      console.error('Error initializing Firebase messaging:', error);
    }

    // Initialize sidebar state
    this.initializeSidebarState();
  }
  private currentToken = '';
  private messaging = inject(Messaging);
  periodicElements = [
    { id: 1, position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' },
  ];
  handleAction(id: number): void {
    console.log('id:', id);
  }

  isSidenavOpen = true;
  isMobileSidenavOpen = false;



  // Initialize sidebar state based on screen size
  private initializeSidebarState(): void {
    if (this.isMobile()) {
      // On mobile, sidebar should be closed by default
      this.isMobileSidenavOpen = false;
      this.isSidenavOpen = true; // Keep this true for desktop logic
    } else {
      // On desktop, sidebar should be open by default
      this.isSidenavOpen = true;
      this.isMobileSidenavOpen = false;
    }
  }

  // Determine if sidebar should be hidden based on screen size and state
  shouldHideSidebar(): boolean {
    if (this.isMobile()) {
      // On mobile, hide sidebar when isMobileSidenavOpen is false
      return !this.isMobileSidenavOpen;
    } else {
      // On desktop, hide sidebar when isSidenavOpen is false
      return !this.isSidenavOpen;
    }
  }

  toggleSidenav() {
    if (this.isMobile()) {
      // On mobile, toggle the mobile sidebar
      this.isMobileSidenavOpen = !this.isMobileSidenavOpen;
    } else {
      // On desktop, toggle the regular sidebar
      this.isSidenavOpen = !this.isSidenavOpen;
    }
  }

  closeMobileSidenav() {
    console.log('closeMobileSidenav called');
    this.isMobileSidenavOpen = false;
  }

  // Check if current screen is mobile
  private isMobile(): boolean {
    return window.innerWidth <= 768;
  }

  // Listen for window resize to handle responsive behavior
  @HostListener('window:resize', ['$event'])
  onResize(event: any): void {
    console.log('Window resized to:', window.innerWidth);

    // Close mobile sidebar if screen becomes desktop size
    if (!this.isMobile() && this.isMobileSidenavOpen) {
      this.closeMobileSidenav();
    }

    // Ensure desktop sidebar is open when switching to desktop
    if (!this.isMobile() && !this.isSidenavOpen) {
      this.isSidenavOpen = true;
    }

    // Initialize sidebar state when switching between mobile/desktop
    this.initializeSidebarState();
  }
  private setupMessageListener() {
  onMessage(this.messaging, (payload:any) => {
    console.log('Message received in AdminLayout:', payload);
    if (payload.notification) {
      let notificationData = {
        title: payload.notification.title || 'إشعار',
        description: payload.notification.body || 'إشعار جديد',
        type: payload.data?.['type'] || 'fundNotifications',
        status: 'active',
        statusId: 1, // customize based on logic
        time: new Date().toLocaleTimeString(),
        createdAt: new Date(),
        date: new Date().toLocaleDateString('en-EG') // or convert Hijri manually
      };

      // Show browser Notification only if granted
      if (Notification.permission === 'granted') {
        try {
          new Notification(notificationData.title, {
            body: notificationData.description,
            icon: '/assets/icons/jadwa-logo.png'
          });
        } catch (err) {
          console.warn('Notification error:', err);
        }
      }

      // Show custom in-app styled notification
      this.notification = notificationData;

      // Optional: auto-hide after 5s
      setTimeout(() => {
        this.notification = null;
      }, 1000);
    }
  });
}

  getIconForType(type: string): string {
    return type === 'fundNotifications'
      ? 'assets/images/notify-green.png'
      : 'assets/images/notify-red.png';
  }


    private showAlertNotification(title: string, body?: string) {
      console.log('Alert notification:', title, body);

      // If you have SweetAlert or another notification library, you can use it here
      // Example (uncomment if you have Swal imported):
      // Swal.fire({
      //   title: title,
      //   text: body || '',
      //   icon: 'info',
      //   toast: true,
      //   position: 'top-end',
      //   showConfirmButton: false,
      //   timer: 3000
      // });
    }

      private async registerServiceWorker() {
        try {
          if ('serviceWorker' in navigator) {
            const registration = await navigator.serviceWorker.register(
              '/firebase-messaging-sw.js',
              {
                scope: '/firebase-cloud-messaging-push-scope',
              }
            );
          } else {
            console.warn('Service workers are not supported in this browser');
          }
        } catch (error) {
          console.error('Error registering service worker:', error);
        }
      }
      private async requestNotificationPermission() {
        try {
          const permission = await Notification.requestPermission();
          console.log('Notification permission status:', permission);
          if (permission === 'granted') {
            console.log('Notification permission granted');
            this.getDeviceToken();
          } else {
            console.log('Notification permission denied');
          }
        } catch (error) {
          console.error('Error requesting notification permission:', error);
        }
      }

      private async getDeviceToken() {
        try {
          this.currentToken = await getToken(this.messaging, {
            vapidKey: environment.firebaseConfig.vapidKey,
          });
          if (this.currentToken) {
            var obj :UpdateFCMTokenCommand = new UpdateFCMTokenCommand();
            obj.fcmWebToken = this.currentToken;
            obj.userId = this.tokenService.getuserId();
            this.AuthService.updateFCMToken(obj).subscribe({

            });
          } else {
            console.warn(
              'No FCM token received. The user may have denied permission.'
            );
          }
        } catch (error) {
          console.error('Error getting FCM token:', error);
        }
      }
}
