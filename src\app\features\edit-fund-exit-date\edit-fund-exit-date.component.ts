import { Component, OnInit, Inject } from '@angular/core';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FormBuilderComponent } from "../../shared/components/form-builder/form-builder.component";
import { TranslateModule } from '@ngx-translate/core';
import { DateConversionService } from '@shared/services/date.service';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
@Component({
  selector: 'app-edit-fund-exit-date',
  standalone: true,
  imports: [FormBuilderComponent,TranslateModule],
  templateUrl: './edit-fund-exit-date.component.html',
  styleUrl: './edit-fund-exit-date.component.scss'
})
export class EditFundExitDateComponent implements OnInit {
  formGroup!: FormGroup;
  isFormSubmitted: boolean | undefined;
  date: any;

  constructor(
    private formBuilder: FormBuilder,
    public dialogRef: MatDialogRef<EditFundExitDateComponent>,
    // Inject MAT_DIALOG_DATA and assign to public property 'data'
    @Inject(MAT_DIALOG_DATA) public data: any,
    private DateConversionService: DateConversionService,
  ) {}

  ngOnInit() {
    this.formControls = [
      {
        type: InputType.Date,
        formControlName: 'initiationDate',
        id: 'initiationDate',
        name: 'initiationDate',
        label: 'FUND_DETAILS.FUND_DETAILS_DATE',
        placeholder: 'FUND_DETAILS.FUND_DETAILS_DATE',
        isRequired: true,
        class: 'col-md-12',
        // Set maxGreg and maxHijri to today's date
        // maxGreg: (() => {
        //   const today = new Date();
        //   return { year: today.getFullYear(), month: today.getMonth() + 1, day: today.getDate() };
        // })(),
        minGreg: (() => {
          const initiationDate = new Date(this.data?.initiationDateGregorian);
          return { year: initiationDate.getFullYear(), month: initiationDate.getMonth() + 1, day: initiationDate.getDate() };
        })(),
        // maxHijri: (() => {
        //   const now = new Date();
        //   const todayStruct = { year: now.getFullYear(), month: now.getMonth() + 1, day: now.getDate() };
        //   const todayHijri = this.DateConversionService.convertGregorianToHijri(todayStruct);
        //   return todayHijri;
        // })(),
        minHijri: (() => {
          const initiationDate = new Date(this.data?.initiationDateGregorian);
          const todayStruct = { year: initiationDate.getFullYear(), month: initiationDate.getMonth() + 1, day: initiationDate.getDate() };
          const todayHijri = this.DateConversionService.convertGregorianToHijri(todayStruct);
          return todayHijri;
        })(),
      },
    ];
    this.initForm()
  }

  // You should pass the fund's initiation date (Gregorian and Hijri) to this component, e.g., via MAT_DIALOG_DATA
  // Assume data.initiationDateGreg and data.initiationDateHijri are objects: { year: number, month: number, day: number }
  formControls: IControlOption[] = [ ];

  private initForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }
      formGroup[control.formControlName] = [null, validators];
    });

    this.formGroup = this.formBuilder.group(formGroup);
  }
  onDateSelected(data:any){
    console.log('on date selec',data);
    this.date = data.event.formattedGregorian
  }

  onSubmit() {

    this.isFormSubmitted = true;
    if(this.date)
    this.dialogRef.close({
      date:this.date,
    });
  }

  onCancel(){
  this.dialogRef.close();
  }
}
