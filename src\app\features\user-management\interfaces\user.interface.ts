export interface IUser {
  id: number;
  fullName: string;
  email: string;
  isActive: boolean;
  role: string;
  lastUpdateDate: Date;
  createdDate?: Date;
  phoneNumber?: string;
  userName?: string;
  countryCode?: string;
  iban?: string;
  nationality?: string;
  passportNo?: string;
  cv?: string;
  personalPhoto?: string;
  registrationMessageIsSent: boolean;
  registrationIsCompleted: boolean;
  roles: string[];
  enRoles: string[];
}

export enum IUserStatus {
  Active = 'active',
  Inactive = 'inactive',
}

export interface IUserTableConfig {
  columns: string[];
  sortable: boolean;
  filterable: boolean;
  paginated: boolean;
}

export interface IUserFilters {
  status?: IUserStatus;
  role?: string;
  name?: string;
  mobileNo?: string;
  searchTerm?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface IUserCreateRequest {
  name: string;
  email: string;
  role: string;
  phoneNumber?: string;
  countryCode?: string;
  iban?: string;
  nationality?: string;
  passportNo?: string;
  cv?: File;
  personalPhoto?: File;
  status?: IUserStatus;
  department?: string;
  permissions?: string[];
}

export interface IUserUpdateRequest extends Partial<IUserCreateRequest> {
  id: number;
  status?: IUserStatus;
}

export interface IUserResponse {
  users: IUser[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
}

export interface IUserActionEvent {
  action: 'view' | 'edit' | 'activate' | 'deactivate' | 'resetPassword' | 'resendMessage';
  user: IUser;
}
