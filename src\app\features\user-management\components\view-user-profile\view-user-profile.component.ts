import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, of, Subject, takeUntil } from 'rxjs';
import { environment } from '../../../../../environments/environment';

// Shared imports
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ErrorModalService } from '@core/services/error-modal.service';

// Core imports
import {
  UserManagementServiceProxy,
  UserProfileResponseDtoBaseResponse,
  AdminResetPasswordCommand,
  ResendRegistrationMessageCommand
} from '@core/api/api.generated';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { UserManagementService } from '@shared/services/users/user-management.service';

// Enums and interfaces
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { SizeEnum } from '@shared/enum/size-enum';

@Component({
  selector: 'app-view-user-profile',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbComponent,
    PageHeaderComponent,
    CustomButtonComponent,
    RouterModule,
    ChangePasswordComponent
  ],
  templateUrl: './view-user-profile.component.html',
  styleUrls: ['./view-user-profile.component.scss']
})
export class ViewUserProfileComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  showChangePassword = false; // Property to control change password form visibility

  breadcrumbItems: IBreadcrumbItem[] = [];

  isLoading = false;
  currentUserData: any = null;
  userId: number | null = null;

  // Additional user data from user list (for missing fields)
  additionalUserData: any = null;

  // Enums for template
  ButtonTypeEnum = ButtonTypeEnum;
breadcrumbSizeEnum =SizeEnum;
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private userManagementService: UserManagementServiceProxy,
    private userManagementServiceShared: UserManagementService,
    private errorModalService: ErrorModalService,
    private tokenService: TokenService
  ) {
    this.initializeBreadcrumbs();
  }

  ngOnInit(): void {
    this.getUserIdFromRoute();
    this.loadUserData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getUserIdFromRoute(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.userId = Number(id);
    } else {
      // If no ID is provided, this is the current user's profile view
      const currentUserId = this.tokenService.getuserId();
      if (currentUserId) {
        this.userId = Number(currentUserId);
      } else {
        this.errorModalService.showError('USER_MANAGEMENT.INVALID_USER_ID');
        this.router.navigate(['/admin/user-management']);
      }
    }
  }

  private initializeBreadcrumbs(): void {
    // Will be updated after we determine if this is current user or not
    this.breadcrumbItems = [
      {
        label: 'USER_PROFILE.UPDATE_PROFILE',
        url: '/admin/dashboard'
      },
      {
        label: 'USER_MANAGEMENT.TITLE',
        url: '/admin/user-management'
      },
      {
        label: 'USER_PROFILE.VIEW_TITLE',
        disabled: true
      }
    ];
  }

  private updateBreadcrumbsForCurrentUser(): void {
    this.breadcrumbItems = [
      {
        label: 'USER_PROFILE.UPDATE_PROFILE',
        url: '/admin/user-management'
      },
      {
        label: 'USER_PROFILE.PAGE_TITLE',
        disabled: true
      }
    ];
  }

  loadUserData(): void {
    this.isLoading = true;

    this.userManagementService
      .getUserProfile(this.userId || undefined)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading user profile:', error);
          this.errorModalService.showError('USER_PROFILE.LOAD_ERROR');
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((response) => {
        if (response) {
          this.currentUserData = response;
          console.log('User data:', this.currentUserData);

          // Load additional user data if viewing another user's profile
          if (!this.isCurrentUser() && this.userId) {
          }

          // Update breadcrumbs if this is current user's profile
          if (this.isCurrentUser()) {
            this.updateBreadcrumbsForCurrentUser();
          }
        }
      });
  }



  // Get user photo URL with fallback to default avatar
  getUserPhotoUrl(): string {
    if (this.currentUserData?.data?.personalPhoto?.filePath?.length > 0) {
      // If the photo is a full URL, use it directly
      if (this.currentUserData?.data?.personalPhoto?.filePath.startsWith('http')) {
        return this.currentUserData?.data?.personalPhoto?.filePath;
      }

      // If it's a relative path, construct the full URL using environment API URL
      const photoPath = this.currentUserData?.data?.personalPhoto ? this.currentUserData?.data?.personalPhoto?.filePath?.startsWith('/')
        ? this.currentUserData?.data?.personalPhoto.filePath
        : `/${this.currentUserData?.data?.personalPhoto}` : '';

      return `${environment.apiUrl}${photoPath}`;
    }
    // Fallback to default avatar
    return 'assets/images/avatar-user.png';
  }

  // Handle image load error - fallback to default avatar
  onImageError(event: any): void {
    event.target.src = 'assets/images/avatar-member.png';
  }

  // Download CV file
  downloadFile(): void {
    const cvFile = this.currentUserData?.data?.cvFile?.filePath;
    let fullUrl: string;
    let fileName: string;

    // Determine the full URL for the CV file
    if (cvFile.startsWith('http')) {
      // If it's already a full URL, use it directly
      fullUrl = cvFile;
    } else {
      // If it's a relative path, construct the full URL
      const filePath = cvFile.startsWith('/') ? cvFile : `/${cvFile}`;
      fullUrl = `${environment.apiUrl}${filePath}`;
    }

    // Extract filename from the URL or use a default name
    fileName = this.extractFileNameFromUrl(cvFile) || 'CV.pdf';

    // Create download link
    const link = document.createElement('a');
    link.href = fullUrl;
    link.target = '_blank'; // Open in new tab as fallback
    link.download = fileName; // Set download filename

    // Add to DOM temporarily to trigger download
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
  }

  // Helper method to extract filename from URL
  private extractFileNameFromUrl(url: string): string | null {
    try {
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1];

      // If filename has an extension, return it
      if (fileName && fileName.includes('.')) {
        return fileName;
      }

      return null;
    } catch (error) {
      console.error('Error extracting filename from URL:', error);
      return null;
    }
  }

  onBack(): void {
    if (this.isCurrentUser()) {
      // If viewing own profile, go back to dashboard
      this.router.navigate(['/admin/dashboard']);
    } else {
      // If viewing another user's profile, go back to user management
      this.router.navigate(['/admin/user-management']);
    }
  }

  // Check if the viewed profile belongs to the current logged-in user
  isCurrentUser(): boolean {
    const currentUserId = this.tokenService.getuserId();
    return !!(currentUserId && this.userId && currentUserId.toString() === this.userId.toString());
  }

  // Navigate to update profile page
  onUpdateProfile(): void {
    this.router.navigate(['/admin/user-management/profile']);
  }
   onToggleChangePassword(): void {
    this.showChangePassword = true;
    this.router.navigate(['/admin/change-password']);

  }

  onPasswordChanged(): void {
    // Password was successfully changed
    this.showChangePassword = false;
    // Optionally show a success message or refresh data
  }

  onChangePasswordCancelled(): void {
    // User cancelled password change
    this.showChangePassword = false;
  }

  // Action methods for super admin
  onEditUser(): void {
    if (this.userId) {
      this.router.navigate(['/admin/user-management/edit', this.userId]);
    }
  }

  onResetPassword(): void {
    if (!this.userId) return;

    const resetCommand = new AdminResetPasswordCommand();
    resetCommand.userId = this.userId;

    this.userManagementService.adminResetPassword(resetCommand)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          return of(null);
        })
      )
      .subscribe((response) => {
        if (response && response.successed) {
          this.errorModalService.showSuccess('USER_MANAGEMENT.RESET_PASSWORD_SUCCESS');
        }
      });
  }

  onResendMessage(): void {
    if (!this.userId) return;

    const resendCommand = new ResendRegistrationMessageCommand();
    resendCommand.userId = this.userId;

    this.userManagementService.resendRegistrationMessage(resendCommand)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          return of(null);
        })
      )
      .subscribe((response) => {
        if (response && response.successed) {
          this.errorModalService.showSuccess('USER_MANAGEMENT.RESEND_MESSAGE_SUCCESS');
        }
      });
  }

  onToggleUserStatus(): void {
    if (!this.userId || !this.currentUserData?.data) return;

    const isCurrentlyActive = this.currentUserData?.data?.isActive;

    if (isCurrentlyActive) {
      // Deactivate user
      this.userManagementServiceShared.deactivateUser(this.userId)
        .pipe(
          takeUntil(this.destroy$),
          catchError(error => {
            return of(null);
          })
        )
        .subscribe((response) => {
          if (response && response.successed) {
            this.currentUserData.data.isActive = false;
            this.errorModalService.showSuccess('USER_MANAGEMENT.DEACTIVATE_SUCCESS');
          }
        });
    } else {
      // Activate user
      const activateData = { userId: this.userId };
      this.userManagementServiceShared.activateUser(activateData)
        .pipe(
          takeUntil(this.destroy$),
          catchError(error => {
            console.error('Error activating user:', error);
            return of(null);
          })
        )
        .subscribe((response) => {
          if (response && response.successed) {
            this.currentUserData.data.isActive = true;
            this.errorModalService.showSuccess('USER_MANAGEMENT.ACTIVATE_SUCCESS');
          }
        });
    }
  }

  // Helper methods
  canShowAdminActions(): boolean {
    return !this.isCurrentUser();
  }

  canResetPassword(): boolean {
    return this.canShowAdminActions() &&
           this.currentUserData?.data?.isActive &&
           this.currentUserData?.data?.registrationIsCompleted;
  }

  canResendMessage(): boolean {
    return this.canShowAdminActions() &&
           this.currentUserData?.data?.isActive;
  }

  getRegistrationStatus(): string {
    if (!this.currentUserData?.data) return '-';

    if (this.currentUserData?.data?.registrationIsCompleted) {
      return 'USER_MANAGEMENT.REGISTRATION.COMPLETED';
    } else {
      return 'USER_MANAGEMENT.REGISTRATION.PENDING';
    }
  }


  getLastUpdateDate(): string {
    if (this.currentUserData?.data?.lastUpdateDate) {
      return new Date(this.currentUserData?.data?.lastUpdateDate).toLocaleDateString();
    }
    return '-';
  }

}
